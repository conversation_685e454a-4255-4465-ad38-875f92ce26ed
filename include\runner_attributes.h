#pragma once

#include "race_simulator.h"

namespace RaceSimulator {
    float CalculateMoodMultiplier(Mood mood);
    Mood GetMoodFromValue(uint8_t moodValue);
    float CalculateAdaptabilityMultiplier(Adaptability adapt, TrackType trackType);
    float CalculateStaminaMultiplier(uint8_t stamina);
    float CalculateEnduranceMultiplier(uint8_t endurance);
    float CalculateReactionTime(const RunnerAttributes* runner);
    float CalculateFalseStartProbability(const RunnerAttributes* runner);
    float CalculateOverallAttributeMultiplier(const RunnerAttributes* runner, const Environment* env);
    float CalculateSkillTriggerProbability(uint8_t intelligence);
}