#pragma once

#include "race_simulator.h"

namespace RaceSimulator {
    
    
    struct MidRaceAccelEffect {
        float speedBoost;       
        float triggerStart;     
        float triggerEnd;       
        bool triggered;
    };

    
    struct StartWisdomEffect {
        float reactionTimeReduction; 
        bool triggered;
    };

    
    struct LateRacePowerEffect {
        float powerBoost;       
        float triggerDistance;  
        bool triggered;
    };

    void CalculateSkillTriggers(const RunnerAttributes* runner, bool skillTriggers[3]);
    float CalculateSkillImpact(const RunnerAttributes* runner, const bool triggers[3], float baseTimes[5], const float segmentDistances[5]);
    StartWisdomEffect CalculateStartWisdom(const RunnerAttributes* runner, bool triggered);
    float ApplyStartWisdom(float reactionTime, const StartWisdomEffect& effect);
    MidRaceAccelEffect CalculateMidRaceAccel(const RunnerAttributes* runner, bool triggered);
    float ApplyMidRaceAccel(float segmentTime, float startDist, float endDist, const MidRaceAccelEffect& effect);
    LateRacePowerEffect CalculateLateRacePower(const RunnerAttributes* runner, bool triggered);
    float ApplyLateRacePower(float segmentTime, float startDist, float endDist, const LateRacePowerEffect& effect, uint8_t power);
}