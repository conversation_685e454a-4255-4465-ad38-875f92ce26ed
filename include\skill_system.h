#pragma once

#include "race_simulator.h"

namespace RaceSimulator {
    // 技能效果结构体
    // 中途加速技能
    struct MidRaceAccelEffect {
        float speedBoost;       // 速度提升 (m/s)
        float triggerStart;     // 触发开始距离 (m)
        float triggerEnd;       // 触发结束距离 (m)
        bool triggered;
    };

    // 起跑智慧技能
    struct StartWisdomEffect {
        float reactionTimeReduction; // 反应时间减少比例
        bool triggered;
    };

    // 后程发力技能
    struct LateRacePowerEffect {
        float powerBoost;       // 力量提升
        float triggerDistance;  // 触发距离 (m)
        bool triggered;
    };

    void CalculateSkillTriggers(const RunnerAttributes* runner, bool skillTriggers);
    float CalculateSkillImpact(const RunnerAttributes* runner, const bool triggers, float baseTimes, const float segmentDistances);
    StartWisdomEffect CalculateStartWisdom(const RunnerAttributes* runner, bool triggered);
    float ApplyStartWisdom(float reactionTime, const StartWisdomEffect& effect);
    MidRaceAccelEffect CalculateMidRaceAccel(const RunnerAttributes* runner, bool triggered);
    float ApplyMidRaceAccel(float segmentTime, float startDist, float endDist, const MidRaceAccelEffect& effect);
    LateRacePowerEffect CalculateLateRacePower(const RunnerAttributes* runner, bool triggered);
    float ApplyLateRacePower(float segmentTime, float startDist, float endDist, const LateRacePowerEffect& effect, uint8_t power);
}