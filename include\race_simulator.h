#pragma once

#ifdef _WIN32
    #ifdef RACE_SIMULATOR_EXPORTS
        #define RACE_SIMULATOR_API __declspec(dllexport)
    #else
        #define RACE_SIMULATOR_API __declspec(dllimport)
    #endif
#else
    #define RACE_SIMULATOR_API __attribute__((visibility("default")))
#endif

#include <cstdint>

#ifdef __cplusplus
#endif

// 心情状态
enum class Mood {
    EXCELLENT = 0,  // 极佳
    GOOD = 1,       // 好
    POOR = 2,       // 不良
    BAD = 3         // 差
};

// 适应性状态
enum class Adaptability {
    EXCELLENT = 0,  // 极佳
    GOOD = 1,       // 好
    POOR = 2,       // 不良
    BAD = 3         // 差
};

// 赛道类型
enum class TrackType {
    PLASTIC = 0,    // 塑胶跑道
    GRASS = 1,      // 草地
    CINDER = 2,     // 煤渣跑道
    DIRT = 3        // 土地
};

// 天气类型
enum class Weather {
    SUNNY = 0,      // 晴
    VERY_SUNNY = 1, // 大晴
    CLOUDY = 2,     // 乌云
    LIGHT_RAIN = 3, // 微雨
    SMALL_RAIN = 4, // 小雨
    MEDIUM_RAIN = 5, // 中雨
    HEAVY_RAIN = 6  // 大雨
};

// 风向
enum class WindDirection {
    HEADWIND = 0,   // 逆风
    TAILWIND = 1    // 顺风
};

// 固有技能类型
enum class InherentSkill {
    MID_RACE_ACCEL = 0,     // 中途加速
    START_WISDOM = 1,       // 起跑智慧
    LATE_RACE_POWER = 2     // 后程发力
};

// 选手属性结构体
struct RunnerAttributes {
    uint8_t mood;           // 心情 (0-255)
    uint8_t stamina;        // 体力 (0-255)
    uint8_t speed;          // 速度 (0-255)
    uint8_t power;          // 力量 (0-255)
    uint8_t endurance;      // 耐力 (0-255)
    uint8_t intelligence;   // 智力 (0-255)
    uint8_t body;           // 身体 (0-255)
    Adaptability adaptability; // 适应性
    bool hasMidRaceAccel;   // 中途加速技能
    bool hasStartWisdom;    // 起跑智慧技能
    bool hasLateRacePower;  // 后程发力技能
};

// 环境属性结构体
struct Environment {
    TrackType trackType;    // 赛道类型
    Weather weather;        // 天气
    WindDirection windDir;  // 风向
    float windSpeed;        // 风速 (m/s)
};

// 比赛结果结构体
struct RaceResult {
    float totalTime;        // 总时间 (秒)
    float reactionTime;     // 起跑反应时间 (秒)
    float segment1Time;     // 0-30m 时间
    float segment2Time;     // 30-60m 时间
    float segment3Time;     // 60-95m 时间
    float segment4Time;     // 95-100m 时间
    bool falseStart;        // 是否抢跑
    bool skillActivated[3]; // 技能触发状态
};

#ifdef __cplusplus
extern "C" {
#endif

// 初始化随机数生成器
RACE_SIMULATOR_API void InitializeRaceSimulator();

// 计算比赛结果
RACE_SIMULATOR_API RaceResult CalculateRaceResult(
    const RunnerAttributes* runner,
    const Environment* env
);

// 获取最后错误信息
RACE_SIMULATOR_API const char* GetLastErrorMessage();

// 设置随机种子（用于测试）
RACE_SIMULATOR_API void SetRandomSeed(uint32_t seed);

#ifdef __cplusplus
}
#endif