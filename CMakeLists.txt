cmake_minimum_required(VERSION 3.15)
project(RaceSimulator VERSION 1.0.0)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置DLL输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# 包含目录
include_directories(include)

# 源文件
set(SOURCES
    src/race_simulator.cpp
    src/runner_attributes.cpp
    src/skill_system.cpp
    src/environment.cpp
    src/random_generator.cpp
)

# 创建DLL
add_library(race_simulator SHARED ${SOURCES})

# 设置Windows DLL导出
if(WIN32)
    target_compile_definitions(race_simulator PRIVATE RACE_SIMULATOR_EXPORTS)
endif()

# 编译器选项
if(MSVC)
    target_compile_options(race_simulator PRIVATE /W4 /utf-8)
else()
    target_compile_options(race_simulator PRIVATE -Wall -Wextra -Wpedantic)
endif()

# 安装
install(TARGETS race_simulator
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
)

install(FILES include/race_simulator.h DESTINATION include)

# 测试
if(BUILD_TESTING)
    enable_testing()
    # Build tests
    add_executable(test_skill_system tests/test_skill_system.cpp)
    target_link_libraries(test_skill_system PRIVATE race_simulator)
endif()